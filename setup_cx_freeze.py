#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Setup script para cx_freeze - Alternativa ao PyInstaller
Para usar: python setup_cx_freeze.py build
"""

import sys
from cx_Freeze import setup, Executable

# Dependências que devem ser incluídas
build_exe_options = {
    "packages": [
        "PyQt6",
        "PyQt6.QtCore",
        "PyQt6.QtGui", 
        "PyQt6.QtWidgets",
        "selenium",
        "selenium.webdriver",
        "selenium.webdriver.chrome",
        "selenium.webdriver.chrome.service",
        "selenium.webdriver.chrome.options",
        "selenium.webdriver.support",
        "selenium.webdriver.support.ui",
        "selenium.webdriver.support.expected_conditions",
        "selenium.webdriver.common.by",
        "webdriver_manager",
        "webdriver_manager.chrome",
        "pandas",
        "openpyxl",
        "PIL",
        "PIL.Image"
    ],
    "excludes": [
        "tkinter",
        "matplotlib",
        "numpy.distutils",
        "scipy",
        "IPython",
        "jupyter",
        "test",
        "unittest"
    ],
    "include_files": [
        ("logo.png", "logo.png"),
        ("file_version_info.txt", "file_version_info.txt")
    ],
    "optimize": 2,
    "build_exe": "build_cx_freeze"
}

# Configuração para Windows
base = None
if sys.platform == "win32":
    base = "Win32GUI"  # Remove console window

# Configuração do executável
executable = Executable(
    script="ui.py",
    base=base,
    target_name="Prospector.exe",
    icon="logo.png"
)

# Setup
setup(
    name="Prospector",
    version="1.0.0",
    description="Captura de Leads do Google Maps",
    author="Seu Nome",
    options={"build_exe": build_exe_options},
    executables=[executable]
)

print("""
╔══════════════════════════════════════════════════════════════╗
║                    CX_FREEZE SETUP                           ║
║              Alternativa ao PyInstaller                     ║
╚══════════════════════════════════════════════════════════════╝

Para compilar:
1. pip install cx_freeze
2. python setup_cx_freeze.py build

O executável estará em: build_cx_freeze/Prospector.exe
""")
