#!/usr/bin/env python3
"""
Script para compilar o Prospector usando --onedir (mais est<PERSON>vel)
Esta abordagem cria uma pasta com o executável e dependências
"""

import subprocess
import sys
import os
import shutil
from pathlib import Path

def main():
    print("🚀 COMPILADOR ONEDIR - PROSPECTOR")
    print("=" * 50)
    print("Criando executável em pasta (mais estável)")
    print()
    
    # Verificar arquivo principal
    if not os.path.exists('ui.py'):
        print("❌ Arquivo ui.py não encontrado!")
        return False
    
    # Limpar builds anteriores
    print("🧹 Limpando builds anteriores...")
    dirs_to_clean = ['build', 'dist', '__pycache__']
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            try:
                shutil.rmtree(dir_name)
                print(f"✅ Removido: {dir_name}")
            except:
                pass
    
    # Remover arquivos .spec
    for spec_file in Path('.').glob('*.spec'):
        try:
            spec_file.unlink()
        except:
            pass
    
    print("\n📦 Verificando PyInstaller...")
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", 
            "pyinstaller", "--upgrade"
        ])
        print("✅ PyInstaller atualizado")
    except:
        print("⚠️ Erro ao atualizar PyInstaller")
    
    print("\n🔨 Compilando com --onedir...")
    
    # Comando usando --onedir (mais estável)
    cmd = [
        'pyinstaller',
        '--onedir',              # Pasta em vez de arquivo único
        '--windowed',            # Sem console
        '--name=Prospector',     # Nome
        '--noconfirm',           # Sem confirmação
        '--clean',               # Limpar cache
        '--distpath=dist',       # Pasta de destino
        '--workpath=build',      # Pasta de trabalho
    ]
    
    # Adicionar recursos se existirem
    if os.path.exists('logo.png'):
        cmd.extend(['--icon=logo.png', '--add-data=logo.png;.'])
        print("✅ Ícone incluído")
    
    if os.path.exists('file_version_info.txt'):
        cmd.append('--version-file=file_version_info.txt')
        print("✅ Informações de versão incluídas")
    
    # Adicionar imports importantes
    important_imports = [
        'PyQt6.QtCore',
        'PyQt6.QtGui', 
        'PyQt6.QtWidgets',
        'selenium',
        'webdriver_manager',
        'pandas',
        'openpyxl'
    ]
    
    for imp in important_imports:
        cmd.append(f'--hidden-import={imp}')
    
    # Arquivo principal
    cmd.append('ui.py')
    
    print(f"📋 Executando PyInstaller...")
    print(f"Comando: pyinstaller --onedir --windowed ...")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)
        
        if result.returncode == 0:
            print("✅ Compilação concluída!")
            
            # Verificar resultado
            exe_path = Path("dist") / "Prospector" / "Prospector.exe"
            if exe_path.exists():
                # Calcular tamanho total da pasta
                total_size = 0
                for file_path in Path("dist/Prospector").rglob("*"):
                    if file_path.is_file():
                        total_size += file_path.stat().st_size
                
                size_mb = total_size / (1024 * 1024)
                
                print(f"📁 Executável: {exe_path}")
                print(f"📏 Tamanho total: {size_mb:.1f} MB")
                print(f"📂 Pasta completa: dist/Prospector/")
                
                # Contar arquivos
                file_count = len(list(Path("dist/Prospector").rglob("*")))
                print(f"📄 Arquivos na pasta: {file_count}")
                
                print("\n🎉 SUCESSO!")
                print("📋 Para distribuir:")
                print("   - Copie toda a pasta 'dist/Prospector'")
                print("   - Execute 'Prospector.exe' dentro da pasta")
                
                return True
            else:
                print("❌ Executável não encontrado")
                return False
        else:
            print("❌ Erro na compilação:")
            # Mostrar últimas linhas do erro
            error_lines = result.stderr.split('\n')
            for line in error_lines[-15:]:
                if line.strip():
                    print(f"   {line}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ Timeout na compilação (>10 minutos)")
        return False
    except Exception as e:
        print(f"❌ Erro inesperado: {e}")
        return False

def create_launcher():
    """Cria um launcher simples para facilitar execução"""
    launcher_content = '''@echo off
cd /d "%~dp0"
cd dist\\Prospector
start Prospector.exe
'''
    
    with open('Executar_Prospector.bat', 'w') as f:
        f.write(launcher_content)
    
    print("✅ Criado: Executar_Prospector.bat")

if __name__ == "__main__":
    success = main()
    
    if success:
        create_launcher()
    
    print("\n" + "="*50)
    if success:
        print("✅ COMPILAÇÃO ONEDIR CONCLUÍDA!")
        print("🎯 Execute: dist/Prospector/Prospector.exe")
        print("🚀 Ou use: Executar_Prospector.bat")
        print("\n📦 Para distribuir:")
        print("   - Comprima a pasta 'dist/Prospector'")
        print("   - Descomprima no computador de destino")
        print("   - Execute Prospector.exe")
    else:
        print("❌ FALHA NA COMPILAÇÃO ONEDIR")
        print("\n💡 Próximos passos:")
        print("1. Execute: python build_fix_error.py")
        print("2. Verifique se todas as dependências estão instaladas")
        print("3. Tente em um ambiente virtual limpo")
    
    input("\nPressione Enter para sair...")
