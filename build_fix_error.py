#!/usr/bin/env python3
"""
Script para corrigir o erro do PyInstaller e compilar o Prospector
Solução para o erro: IndexError: tuple index out of range
"""

import subprocess
import sys
import os
import shutil
from pathlib import Path

def clean_environment():
    """Limpa o ambiente Python de arquivos problemáticos"""
    print("🧹 Limpando ambiente...")
    
    # Limpar cache do Python
    dirs_to_clean = ['__pycache__', '.pytest_cache', 'build', 'dist']
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            try:
                shutil.rmtree(dir_name)
                print(f"✅ Removido: {dir_name}")
            except:
                pass
    
    # Remover arquivos .pyc
    for pyc_file in Path('.').rglob('*.pyc'):
        try:
            pyc_file.unlink()
        except:
            pass
    
    # Remover arquivos .spec
    for spec_file in Path('.').glob('*.spec'):
        try:
            spec_file.unlink()
            print(f"✅ Removido: {spec_file}")
        except:
            pass

def fix_pyinstaller():
    """Corrige problemas do PyInstaller"""
    print("🔧 Corrigindo PyInstaller...")
    
    try:
        # Desinstalar PyInstaller atual
        subprocess.run([sys.executable, "-m", "pip", "uninstall", "pyinstaller", "-y"], 
                      capture_output=True)
        
        # Instalar versão específica que funciona melhor
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", 
            "pyinstaller==5.13.2", "--force-reinstall"
        ])
        print("✅ PyInstaller 5.13.2 instalado")
        return True
    except:
        try:
            # Tentar versão ainda mais antiga se necessário
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", 
                "pyinstaller==5.10.1", "--force-reinstall"
            ])
            print("✅ PyInstaller 5.10.1 instalado")
            return True
        except:
            print("❌ Erro ao instalar PyInstaller")
            return False

def create_minimal_ui():
    """Cria uma versão mínima do ui.py para teste"""
    print("📝 Criando versão de teste...")
    
    minimal_code = '''
import sys
from PyQt6.QtWidgets import QApplication, QMainWindow, QLabel, QVBoxLayout, QWidget

class MinimalApp(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Prospector - Teste")
        self.setGeometry(100, 100, 400, 300)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout()
        label = QLabel("Prospector funcionando!")
        layout.addWidget(label)
        central_widget.setLayout(layout)

def main():
    app = QApplication(sys.argv)
    window = MinimalApp()
    window.show()
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
'''
    
    with open('ui_test.py', 'w', encoding='utf-8') as f:
        f.write(minimal_code)
    
    print("✅ ui_test.py criado")

def compile_minimal():
    """Compila a versão mínima primeiro"""
    print("🔨 Compilando versão de teste...")
    
    cmd = [
        'pyinstaller',
        '--onefile',
        '--windowed',
        '--name=ProspectorTest',
        '--noconfirm',
        '--clean',
        'ui_test.py'
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=180)
        
        if result.returncode == 0:
            print("✅ Teste compilado com sucesso!")
            return True
        else:
            print("❌ Erro no teste:")
            print(result.stderr[-500:])  # Últimas linhas do erro
            return False
    except subprocess.TimeoutExpired:
        print("❌ Timeout no teste")
        return False

def compile_full():
    """Compila a versão completa"""
    print("🔨 Compilando versão completa...")
    
    cmd = [
        'pyinstaller',
        '--onefile',
        '--windowed',
        '--name=Prospector',
        '--noconfirm',
        '--clean',
        '--exclude-module=matplotlib',
        '--exclude-module=tkinter',
        '--exclude-module=IPython',
        '--exclude-module=jupyter',
        'ui.py'
    ]
    
    # Adicionar ícone se existir
    if os.path.exists('logo.png'):
        cmd.insert(-1, '--icon=logo.png')
        cmd.insert(-1, '--add-data=logo.png;.')
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print("✅ Compilação completa bem-sucedida!")
            
            # Verificar arquivo
            exe_path = Path("dist") / "Prospector.exe"
            if exe_path.exists():
                size_mb = exe_path.stat().st_size / (1024 * 1024)
                print(f"📁 Executável: {exe_path}")
                print(f"📏 Tamanho: {size_mb:.1f} MB")
                return True
            else:
                print("❌ Executável não encontrado")
                return False
        else:
            print("❌ Erro na compilação completa:")
            print(result.stderr[-1000:])  # Últimas linhas do erro
            return False
    except subprocess.TimeoutExpired:
        print("❌ Timeout na compilação")
        return False

def main():
    print("🚀 CORRETOR DE ERRO PYINSTALLER - PROSPECTOR")
    print("=" * 60)
    print("Este script corrige o erro 'IndexError: tuple index out of range'")
    print()
    
    # Passo 1: Limpar ambiente
    clean_environment()
    
    # Passo 2: Corrigir PyInstaller
    if not fix_pyinstaller():
        print("❌ Não foi possível corrigir o PyInstaller")
        return False
    
    # Passo 3: Teste com versão mínima
    create_minimal_ui()
    if not compile_minimal():
        print("❌ Falha no teste básico")
        return False
    
    # Passo 4: Compilar versão completa
    if not compile_full():
        print("❌ Falha na compilação completa")
        return False
    
    # Limpeza final
    if os.path.exists('ui_test.py'):
        os.remove('ui_test.py')
    
    print("\n🎉 SUCESSO TOTAL!")
    print("📂 Executável disponível em: dist/Prospector.exe")
    return True

if __name__ == "__main__":
    success = main()
    
    print("\n" + "="*60)
    if success:
        print("✅ PROBLEMA RESOLVIDO!")
        print("🎯 Execute: dist/Prospector.exe")
    else:
        print("❌ AINDA HÁ PROBLEMAS")
        print("\n💡 Soluções alternativas:")
        print("1. Atualizar Python para versão mais recente")
        print("2. Usar ambiente virtual limpo")
        print("3. Compilar em modo --onedir em vez de --onefile")
    
    input("\nPressione Enter para sair...")
