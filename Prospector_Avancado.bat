@echo off
chcp 65001 >nul
title Prospector - Captura de Leads do Google Maps

:: Configurações
set APP_NAME=Prospector
set MAIN_FILE=ui.py
set REQUIREMENTS_FILE=requirements.txt
set VENV_NAME=prospector_env

:: Cores (para Windows 10+)
set GREEN=[92m
set RED=[91m
set YELLOW=[93m
set BLUE=[94m
set CYAN=[96m
set WHITE=[97m
set RESET=[0m

:: Função para imprimir com cor
goto :main

:print_colored
echo %~2%~1%RESET%
goto :eof

:main
cls
echo.
echo %CYAN%╔══════════════════════════════════════════════════════════════╗%RESET%
echo %CYAN%║%WHITE%                        PROSPECTOR                            %CYAN%║%RESET%
echo %CYAN%║%WHITE%              Captura de Leads do Google Maps                %CYAN%║%RESET%
echo %CYAN%║%WHITE%                     Versão 1.0.0                           %CYAN%║%RESET%
echo %CYAN%╚══════════════════════════════════════════════════════════════╝%RESET%
echo.

:: Verificar se Python está instalado
echo %BLUE%[1/5]%WHITE% Verificando Python...%RESET%
python --version >nul 2>&1
if errorlevel 1 (
    echo %RED%❌ Python não encontrado!%RESET%
    echo %YELLOW%💡 Instale o Python em: https://www.python.org/downloads/%RESET%
    echo.
    pause
    exit /b 1
)

for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
echo %GREEN%✅ Python %PYTHON_VERSION% encontrado%RESET%

:: Verificar arquivo principal
echo %BLUE%[2/5]%WHITE% Verificando arquivos do projeto...%RESET%
if not exist "%MAIN_FILE%" (
    echo %RED%❌ Arquivo %MAIN_FILE% não encontrado!%RESET%
    echo %YELLOW%💡 Certifique-se de que este .bat está na pasta do projeto%RESET%
    pause
    exit /b 1
)
echo %GREEN%✅ Arquivo principal encontrado%RESET%

:: Verificar se existe ambiente virtual
echo %BLUE%[3/5]%WHITE% Verificando ambiente virtual...%RESET%
if exist "%VENV_NAME%" (
    echo %GREEN%✅ Ambiente virtual encontrado%RESET%
    set PYTHON_CMD=%VENV_NAME%\Scripts\python.exe
    set PIP_CMD=%VENV_NAME%\Scripts\pip.exe
) else (
    echo %YELLOW%⚠️  Ambiente virtual não encontrado%RESET%
    echo %CYAN%🔄 Criando ambiente virtual...%RESET%
    
    python -m venv %VENV_NAME%
    if errorlevel 1 (
        echo %RED%❌ Falha ao criar ambiente virtual%RESET%
        echo %YELLOW%💡 Usando Python global%RESET%
        set PYTHON_CMD=python
        set PIP_CMD=pip
    ) else (
        echo %GREEN%✅ Ambiente virtual criado%RESET%
        set PYTHON_CMD=%VENV_NAME%\Scripts\python.exe
        set PIP_CMD=%VENV_NAME%\Scripts\pip.exe
    )
)

:: Verificar e instalar dependências
echo %BLUE%[4/5]%WHITE% Verificando dependências...%RESET%
%PYTHON_CMD% -c "import PyQt6, selenium, pandas, webdriver_manager, openpyxl, PIL" >nul 2>&1
if errorlevel 1 (
    echo %YELLOW%⚠️  Dependências faltando%RESET%
    
    if exist "%REQUIREMENTS_FILE%" (
        echo %CYAN%🔄 Instalando dependências...%RESET%
        %PIP_CMD% install -r %REQUIREMENTS_FILE% --quiet
        if errorlevel 1 (
            echo %RED%❌ Falha ao instalar dependências%RESET%
            echo %YELLOW%💡 Execute manualmente: pip install -r requirements.txt%RESET%
            pause
            exit /b 1
        )
        echo %GREEN%✅ Dependências instaladas%RESET%
    ) else (
        echo %YELLOW%⚠️  Arquivo requirements.txt não encontrado%RESET%
        echo %CYAN%🔄 Instalando dependências básicas...%RESET%
        %PIP_CMD% install PyQt6 selenium pandas webdriver-manager openpyxl pillow --quiet
        if errorlevel 1 (
            echo %RED%❌ Falha ao instalar dependências básicas%RESET%
            pause
            exit /b 1
        )
        echo %GREEN%✅ Dependências básicas instaladas%RESET%
    )
) else (
    echo %GREEN%✅ Todas as dependências estão instaladas%RESET%
)

:: Executar aplicativo
echo %BLUE%[5/5]%WHITE% Iniciando %APP_NAME%...%RESET%
echo.
echo %CYAN%🚀 Abrindo aplicativo...%RESET%
echo %YELLOW%💡 Para fechar, use Ctrl+C ou feche a janela do aplicativo%RESET%
echo.

:: Executar o aplicativo
%PYTHON_CMD% "%MAIN_FILE%"

:: Verificar resultado
if errorlevel 1 (
    echo.
    echo %RED%❌ O aplicativo encerrou com erro%RESET%
    echo %YELLOW%💡 Possíveis soluções:%RESET%
    echo %WHITE%   • Verifique se o Google Chrome está instalado%RESET%
    echo %WHITE%   • Verifique sua conexão com internet%RESET%
    echo %WHITE%   • Execute como administrador%RESET%
    echo.
    pause
) else (
    echo.
    echo %GREEN%✅ %APP_NAME% encerrado normalmente%RESET%
    echo %CYAN%💡 Obrigado por usar o Prospector!%RESET%
    timeout /t 3 >nul
)

exit /b 0
