#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Launcher para o Prospector
Alternativa ao arquivo .bat para sistemas que não suportam batch
"""

import os
import sys
import subprocess
import importlib.util

def check_module(module_name, package_name=None):
    """Verifica se um módulo está instalado"""
    try:
        if package_name:
            importlib.import_module(package_name)
        else:
            importlib.import_module(module_name)
        return True
    except ImportError:
        return False

def install_package(package):
    """Instala um pacote usando pip"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        return True
    except subprocess.CalledProcessError:
        return False

def main():
    print("=" * 60)
    print("                    PROSPECTOR LAUNCHER")
    print("              Captura de Leads do Google Maps")
    print("=" * 60)
    print()
    
    # Verificar Python
    print("🐍 Verificando Python...")
    print(f"   Versão: {sys.version}")
    
    if sys.version_info < (3, 8):
        print("❌ Python 3.8+ é necessário")
        input("Pressione Enter para sair...")
        return False
    
    print("✅ Versão do Python adequada")
    
    # Verificar arquivo principal
    print("\n📁 Verificando arquivos...")
    if not os.path.exists("ui.py"):
        print("❌ Arquivo ui.py não encontrado!")
        print("💡 Certifique-se de que este script está na pasta do projeto")
        input("Pressione Enter para sair...")
        return False
    
    print("✅ Arquivo principal encontrado")
    
    # Verificar dependências
    print("\n📦 Verificando dependências...")
    
    dependencies = [
        ("PyQt6", "PyQt6.QtWidgets"),
        ("selenium", "selenium"),
        ("pandas", "pandas"),
        ("webdriver_manager", "webdriver_manager"),
        ("openpyxl", "openpyxl"),
        ("PIL", "PIL")
    ]
    
    missing_deps = []
    
    for display_name, import_name in dependencies:
        if check_module(display_name, import_name):
            print(f"✅ {display_name}")
        else:
            print(f"❌ {display_name} - NÃO INSTALADO")
            missing_deps.append(display_name)
    
    # Instalar dependências faltantes
    if missing_deps:
        print(f"\n⚠️  Dependências faltando: {', '.join(missing_deps)}")
        
        if os.path.exists("requirements.txt"):
            print("🔄 Instalando dependências do requirements.txt...")
            try:
                subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
                print("✅ Dependências instaladas com sucesso!")
            except subprocess.CalledProcessError:
                print("❌ Falha ao instalar dependências")
                print("💡 Execute manualmente: pip install -r requirements.txt")
                input("Pressione Enter para sair...")
                return False
        else:
            print("🔄 Instalando dependências básicas...")
            basic_packages = ["PyQt6", "selenium", "pandas", "webdriver-manager", "openpyxl", "pillow"]
            
            for package in basic_packages:
                if package.lower() in [dep.lower() for dep in missing_deps]:
                    print(f"   Instalando {package}...")
                    if not install_package(package):
                        print(f"❌ Falha ao instalar {package}")
                        input("Pressione Enter para sair...")
                        return False
            
            print("✅ Dependências básicas instaladas!")
    
    # Executar aplicativo
    print("\n🚀 Iniciando Prospector...")
    print("💡 Para fechar, feche a janela do aplicativo ou use Ctrl+C")
    print()
    
    try:
        # Importar e executar o módulo principal
        import ui
        print("✅ Prospector iniciado com sucesso!")
        
    except ImportError as e:
        print(f"❌ Erro ao importar módulo principal: {e}")
        print("💡 Verifique se todas as dependências estão instaladas")
        input("Pressione Enter para sair...")
        return False
    except KeyboardInterrupt:
        print("\n⚠️  Aplicativo interrompido pelo usuário")
        return True
    except Exception as e:
        print(f"❌ Erro inesperado: {e}")
        print("💡 Possíveis soluções:")
        print("   • Verifique se o Google Chrome está instalado")
        print("   • Verifique sua conexão com internet")
        print("   • Execute como administrador")
        input("Pressione Enter para sair...")
        return False
    
    print("\n✅ Prospector encerrado normalmente")
    print("💡 Obrigado por usar o Prospector!")
    return True

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n\n⚠️  Operação cancelada pelo usuário")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Erro crítico: {e}")
        input("Pressione Enter para sair...")
        sys.exit(1)
