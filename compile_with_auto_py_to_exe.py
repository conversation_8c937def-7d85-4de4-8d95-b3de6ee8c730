#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para compilar o Prospector usando auto-py-to-exe
Solução definitiva para o bug do PyInstaller com Python 3.10
"""

import os
import sys
import subprocess
import json

def run_cmd(cmd, description=""):
    """Executa comando e mostra resultado"""
    if description:
        print(f"\n🔄 {description}...")
    
    print(f"Executando: {cmd}")
    result = subprocess.run(cmd, shell=True)
    
    if result.returncode == 0:
        print(f"✅ {description} - Sucesso")
        return True
    else:
        print(f"❌ {description} - Falha (código: {result.returncode})")
        return False

def create_auto_py_to_exe_config():
    """Cria arquivo de configuração para auto-py-to-exe"""
    config = {
        "version": "auto-py-to-exe-configuration_v1",
        "pyinstallerOptions": [
            {
                "optionDest": "filenames",
                "value": "ui.py"
            },
            {
                "optionDest": "onefile",
                "value": True
            },
            {
                "optionDest": "console",
                "value": False
            },
            {
                "optionDest": "icon_file",
                "value": "logo.png"
            },
            {
                "optionDest": "name",
                "value": "Prospector"
            },
            {
                "optionDest": "add_data",
                "value": "logo.png;."
            },
            {
                "optionDest": "hidden_import",
                "value": [
                    "PyQt6.QtCore",
                    "PyQt6.QtGui",
                    "PyQt6.QtWidgets",
                    "selenium",
                    "selenium.webdriver",
                    "pandas",
                    "openpyxl",
                    "PIL"
                ]
            }
        ],
        "nonPyinstallerOptions": {
            "increaseRecursionLimit": True,
            "manualArguments": ""
        }
    }
    
    with open("auto_py_to_exe_config.json", "w", encoding="utf-8") as f:
        json.dump(config, f, indent=2)
    
    print("✅ Arquivo de configuração criado: auto_py_to_exe_config.json")

def main():
    print("=" * 70)
    print("  COMPILADOR PROSPECTOR - AUTO-PY-TO-EXE")
    print("=" * 70)
    print("Solução definitiva para o bug do PyInstaller com Python 3.10")
    print()
    
    # 1. Verificar arquivos necessários
    print("1. Verificando arquivos...")
    required_files = ["ui.py", "logo.png"]
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ {file} - NÃO ENCONTRADO")
            return False
    
    # 2. Instalar auto-py-to-exe
    print("\n2. Instalando auto-py-to-exe...")
    if not run_cmd("pip install auto-py-to-exe", "Instalando auto-py-to-exe"):
        print("❌ Falha ao instalar auto-py-to-exe")
        return False
    
    # 3. Criar arquivo de configuração
    print("\n3. Criando configuração...")
    create_auto_py_to_exe_config()
    
    # 4. Instruções para o usuário
    print("\n" + "="*70)
    print("🎯 PRÓXIMOS PASSOS:")
    print("="*70)
    print()
    print("1. Execute o comando abaixo para abrir a interface gráfica:")
    print("   auto-py-to-exe")
    print()
    print("2. Na interface que abrir:")
    print("   ✅ Script Location: ui.py")
    print("   ✅ Onefile: One File")
    print("   ✅ Console Window: Window Based (Hide the console)")
    print("   ✅ Icon: logo.png")
    print("   ✅ Additional Files: logo.png")
    print()
    print("3. Clique em 'CONVERT .PY TO .EXE'")
    print()
    print("4. Aguarde a compilação (5-10 minutos)")
    print()
    print("5. O executável estará em: output/Prospector.exe")
    print()
    print("="*70)
    print("💡 DICA: Use o arquivo 'auto_py_to_exe_config.json' para")
    print("   carregar as configurações automaticamente!")
    print("="*70)
    
    # 5. Perguntar se quer abrir automaticamente
    print("\nDeseja abrir o auto-py-to-exe agora? (S/N): ", end="")
    choice = input().strip().upper()
    
    if choice == "S":
        print("\n🚀 Abrindo auto-py-to-exe...")
        try:
            subprocess.Popen("auto-py-to-exe", shell=True)
            print("✅ Interface gráfica aberta!")
            print("\n📋 Lembre-se de:")
            print("   1. Carregar o arquivo de configuração se necessário")
            print("   2. Verificar todas as configurações")
            print("   3. Clicar em 'CONVERT .PY TO .EXE'")
        except Exception as e:
            print(f"❌ Erro ao abrir interface: {e}")
            print("Execute manualmente: auto-py-to-exe")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n✨ Configuração concluída!")
            print("🎯 Use auto-py-to-exe para compilar o executável")
        else:
            print("\n💥 Falha na configuração!")
        input("\nPressione Enter para sair...")
    except KeyboardInterrupt:
        print("\n\n⚠️ Operação cancelada pelo usuário")
    except Exception as e:
        print(f"\n❌ Erro: {e}")
        input("\nPressione Enter para sair...")
