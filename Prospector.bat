@echo off
chcp 65001 >nul
title Prospector - Captura de Leads do Google Maps

:: Configurações
set APP_NAME=Prospector
set MAIN_FILE=ui.py
set PYTHON_CMD=python

:: Cabeçalho
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                        PROSPECTOR                            ║
echo ║              Captura de Leads do Google Maps                ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

:: Verificar se Python está instalado
echo [INFO] Verificando Python...
%PYTHON_CMD% --version >nul 2>&1
if errorlevel 1 (
    echo [ERRO] Python não encontrado!
    echo [INFO] Instale o Python em: https://www.python.org/downloads/
    echo.
    pause
    exit /b 1
)

:: Verificar se o arquivo principal existe
if not exist "%MAIN_FILE%" (
    echo [ERRO] Arquivo %MAIN_FILE% não encontrado!
    echo [INFO] Certifique-se de que este arquivo .bat está na mesma pasta do projeto.
    echo.
    pause
    exit /b 1
)

:: Verificar dependências
echo [INFO] Verificando dependências...
%PYTHON_CMD% -c "import PyQt6, selenium, pandas, webdriver_manager, openpyxl, PIL" >nul 2>&1
if errorlevel 1 (
    echo [AVISO] Algumas dependências podem estar faltando.
    echo [INFO] Instalando dependências automaticamente...
    echo.
    
    :: Tentar instalar dependências
    %PYTHON_CMD% -m pip install -r requirements.txt
    if errorlevel 1 (
        echo [ERRO] Falha ao instalar dependências.
        echo [INFO] Execute manualmente: pip install -r requirements.txt
        echo.
        pause
        exit /b 1
    )
    echo [SUCESSO] Dependências instaladas!
)

:: Executar aplicativo
echo [INFO] Iniciando %APP_NAME%...
echo.

:: Executar o aplicativo Python
%PYTHON_CMD% "%MAIN_FILE%"

:: Verificar se houve erro na execução
if errorlevel 1 (
    echo.
    echo [ERRO] O aplicativo encerrou com erro.
    echo [INFO] Verifique se todas as dependências estão instaladas.
    echo.
    pause
) else (
    echo.
    echo [INFO] %APP_NAME% encerrado normalmente.
)

exit /b 0
