# 🚀 COMO EXECUTAR O PROSPECTOR

## 📋 OPÇÕES DISPONÍVEIS

Criamos **3 formas diferentes** de executar o Prospector sem precisar compilar:

### 🥇 OPÇÃO 1: Arquivo Batch Simples (Windows)
```bash
# Duplo clique no arquivo:
Prospector.bat
```

### 🥈 OPÇÃO 2: Arquivo Batch Avançado (Windows)
```bash
# Duplo clique no arquivo:
Prospector_Avancado.bat
```

### 🥉 OPÇÃO 3: Script Python (Multiplataforma)
```bash
# Execute no terminal:
python run_prospector.py
```

## 🔧 DIFERENÇAS ENTRE AS OPÇÕES

| Recurso | Simples | Avançado | Python |
|---------|---------|----------|---------|
| **Verificação Python** | ✅ | ✅ | ✅ |
| **Verificação arquivos** | ✅ | ✅ | ✅ |
| **Instalação automática** | ✅ | ✅ | ✅ |
| **Ambiente virtual** | ❌ | ✅ | ❌ |
| **Interface colorida** | ❌ | ✅ | ❌ |
| **Multiplataforma** | ❌ | ❌ | ✅ |
| **Tratamento de erros** | Básico | Avançado | Avançado |

## 🎯 RECOMENDAÇÃO

### Para Windows:
**Use o `Prospector_Avancado.bat`** - Tem todas as funcionalidades e é mais robusto.

### Para Linux/Mac:
**Use o `run_prospector.py`** - Funciona em qualquer sistema operacional.

## 📁 ESTRUTURA DE ARQUIVOS

Certifique-se de que sua pasta tenha esta estrutura:

```
📂 PROSPETOR_DESKTOP/
├── 📄 ui.py                    # Arquivo principal
├── 📄 logic_bot.py            # Lógica do bot
├── 📄 splash_screen.py        # Tela de splash
├── 📄 about_dialog.py         # Diálogo sobre
├── 📄 resource_path.py        # Recursos
├── 📄 requirements.txt        # Dependências
├── 🖼️ logo.png               # Ícone do app
├── 📄 Prospector.bat          # ⭐ Launcher simples
├── 📄 Prospector_Avancado.bat # ⭐ Launcher avançado
├── 📄 run_prospector.py       # ⭐ Launcher Python
└── 📄 README_EXECUCAO.md      # Este arquivo
```

## 🔧 INSTALAÇÃO DE DEPENDÊNCIAS

Os launchers instalam automaticamente, mas se precisar fazer manualmente:

```bash
# Instalar dependências
pip install -r requirements.txt

# Ou instalar individualmente:
pip install PyQt6 selenium pandas webdriver-manager openpyxl pillow
```

## ⚠️ SOLUÇÃO DE PROBLEMAS

### Problema: "Python não encontrado"
**Solução:** Instale Python em https://www.python.org/downloads/

### Problema: "Dependências faltando"
**Solução:** Execute `pip install -r requirements.txt`

### Problema: "Google Chrome não encontrado"
**Solução:** Instale Google Chrome

### Problema: "Erro de permissão"
**Solução:** Execute como administrador

## 🎉 VANTAGENS DESTA SOLUÇÃO

✅ **Sem compilação** - Não precisa gerar executável
✅ **Mais rápido** - Inicia imediatamente
✅ **Menos espaço** - Não duplica dependências
✅ **Mais flexível** - Fácil de modificar e atualizar
✅ **Sem bugs** - Evita problemas do PyInstaller
✅ **Multiplataforma** - Funciona em Windows, Linux e Mac

## 🚀 COMO USAR

1. **Escolha um launcher:**
   - Windows: `Prospector_Avancado.bat`
   - Linux/Mac: `run_prospector.py`

2. **Execute:**
   - Duplo clique no .bat (Windows)
   - `python run_prospector.py` (Terminal)

3. **Aguarde:**
   - O launcher verifica tudo automaticamente
   - Instala dependências se necessário
   - Abre o Prospector

4. **Use normalmente:**
   - Configure os parâmetros
   - Inicie a captura de leads
   - Exporte os resultados

## 💡 DICAS

- **Primeira execução:** Pode demorar mais (instalação de dependências)
- **Execuções seguintes:** Muito mais rápidas
- **Atualizações:** Só precisa atualizar os arquivos .py
- **Distribuição:** Envie toda a pasta para outros usuários

---

**🎯 Esta solução é muito melhor que compilar um executável!**
