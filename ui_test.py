
import sys
from PyQt6.QtWidgets import <PERSON><PERSON><PERSON><PERSON>, QMainWindow, QLabel, QVBoxLayout, QWidget

class MinimalApp(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Prospector - Teste")
        self.setGeometry(100, 100, 400, 300)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout()
        label = QLabel("Prospector funcionando!")
        layout.addWidget(label)
        central_widget.setLayout(layout)

def main():
    app = QApplication(sys.argv)
    window = MinimalApp()
    window.show()
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
