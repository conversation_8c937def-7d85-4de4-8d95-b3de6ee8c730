#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de teste para verificar se o ambiente está pronto para compilação
Autor: Augment Agent
"""

import sys
import os
import importlib.util

def test_python_version():
    """Testa a versão do Python"""
    print("🐍 Testando versão do Python...")
    version = sys.version_info
    print(f"   Versão: {version.major}.{version.minor}.{version.micro}")
    
    if version >= (3, 8):
        print("   ✅ Versão adequada")
        return True
    else:
        print("   ❌ Versão inadequada (necessário 3.8+)")
        return False

def test_required_files():
    """Testa se os arquivos necessários existem"""
    print("\n📁 Testando arquivos necessários...")
    
    required_files = [
        "ui.py",
        "logic_bot.py",
        "splash_screen.py", 
        "about_dialog.py",
        "resource_path.py",
        "requirements.txt",
        "logo.png",
        "file_version_info.txt"
    ]
    
    all_found = True
    for file in required_files:
        if os.path.exists(file):
            print(f"   ✅ {file}")
        else:
            print(f"   ❌ {file} - NÃO ENCONTRADO")
            all_found = False
    
    return all_found

def test_module_import(module_name, package_name=None):
    """Testa se um módulo pode ser importado"""
    try:
        if package_name:
            __import__(package_name)
        else:
            __import__(module_name)
        return True
    except ImportError:
        return False

def test_dependencies():
    """Testa se as dependências estão instaladas"""
    print("\n📦 Testando dependências...")
    
    dependencies = [
        ("PyQt6", "PyQt6.QtWidgets"),
        ("selenium", "selenium"),
        ("pandas", "pandas"),
        ("webdriver_manager", "webdriver_manager"),
        ("openpyxl", "openpyxl"),
        ("pyinstaller", "PyInstaller"),
        ("PIL", "PIL")
    ]
    
    all_installed = True
    for display_name, import_name in dependencies:
        if test_module_import(display_name, import_name):
            print(f"   ✅ {display_name}")
        else:
            print(f"   ❌ {display_name} - NÃO INSTALADO")
            all_installed = False
    
    return all_installed

def test_venv_capability():
    """Testa se o venv pode ser criado"""
    print("\n🔧 Testando capacidade de criar ambiente virtual...")
    
    try:
        import venv
        print("   ✅ Módulo venv disponível")
        return True
    except ImportError:
        print("   ❌ Módulo venv não disponível")
        return False

def main():
    """Função principal"""
    print("╔══════════════════════════════════════════════════════════════╗")
    print("║                    TESTE DE AMBIENTE                         ║")
    print("║              Verificação antes da compilação                ║")
    print("╚══════════════════════════════════════════════════════════════╝")
    
    tests = [
        ("Versão do Python", test_python_version),
        ("Arquivos necessários", test_required_files),
        ("Dependências", test_dependencies),
        ("Capacidade de venv", test_venv_capability)
    ]
    
    all_passed = True
    
    for test_name, test_func in tests:
        try:
            if not test_func():
                all_passed = False
        except Exception as e:
            print(f"   ❌ Erro no teste '{test_name}': {e}")
            all_passed = False
    
    print("\n" + "="*60)
    
    if all_passed:
        print("🎉 TODOS OS TESTES PASSARAM!")
        print("✅ O ambiente está pronto para compilação")
        print("\n🚀 Próximo passo: Execute 'python build_venv.py' ou 'build_venv.bat'")
    else:
        print("❌ ALGUNS TESTES FALHARAM!")
        print("⚠️  Corrija os problemas antes de tentar compilar")
        print("\n💡 Dicas:")
        print("   • Instale dependências: pip install -r requirements.txt")
        print("   • Verifique se todos os arquivos estão presentes")
        print("   • Use Python 3.8 ou superior")
    
    print("\n" + "="*60)
    return all_passed

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⚠️  Teste cancelado pelo usuário")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Erro crítico: {e}")
        sys.exit(1)
